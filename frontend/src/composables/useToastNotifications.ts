import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import echo from '@/services/echo'
import toastService, { type ToastNotification } from '@/services/toast'

export function useToastNotifications() {
  const authStore = useAuthStore()
  const isListening = ref(false)
  let privateChannel: any = null

  const startListening = () => {
    if (!authStore.isAuthenticated || !authStore.user || isListening.value) {
      console.log('Toast Notifications: Cannot start listening - auth:', authStore.isAuthenticated, 'user:', !!authStore.user, 'already listening:', isListening.value)
      return
    }

    console.log('Toast Notifications: Setting up private channel listener for user', authStore.user.id)

    try {
      // Listen to private user channel
      privateChannel = echo.private(`user.${authStore.user.id}`)

      // Add subscription success/error handlers
      privateChannel.subscribed(() => {
        console.log('Toast Notifications: Successfully subscribed to private channel user.' + authStore.user?.id)
        isListening.value = true
      })

      privateChannel.error((error: any) => {
        console.error('Toast Notifications: Private channel subscription error:', error)
        isListening.value = false
      })

      privateChannel.listen('.toast.notification', (e: ToastNotification) => {
        console.log('Toast Notifications: Received notification:', e)
        toastService.handleNotification(e)
      })

      console.log('Toast Notifications: Private channel setup initiated for user.' + authStore.user.id)
    } catch (error) {
      console.error('Toast Notifications: Failed to connect to private channel:', error)
      isListening.value = false
    }
  }

  const stopListening = () => {
    if (privateChannel && authStore.user) {
      console.log('Toast Notifications: Disconnecting from private channel')
      echo.leave(`user.${authStore.user.id}`)
      privateChannel = null
      isListening.value = false
    }
  }

  const reconnect = () => {
    stopListening()
    setTimeout(() => {
      startListening()
    }, 1000)
  }

  return {
    isListening,
    startListening,
    stopListening,
    reconnect
  }
}
